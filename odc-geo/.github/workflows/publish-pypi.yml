name: Publish to <PERSON>y<PERSON>

on:
  workflow_dispatch:

jobs:
  publish-pypi:
    if: github.repository == 'opendatacube/odc-geo'

    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Download wheels from artifacts
        uses: actions/download-artifact@v4
        with:
          name: wheels
          path: ./wheels/clean

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install Twine
        run: |
          python -m pip install --upgrade pip
          python -m pip install --upgrade setuptools
          python -m pip install --upgrade \
           toml \
           wheel \
           twine
          python -m pip freeze

      - name: Upload to PyPI
        if: github.event_name == 'workflow_dispatch' || github.ref_type == 'tag'
        env:
          TWINE_PASSWORD: ${{ secrets.PYPI_TOKEN }}
          TWINE_USERNAME: __token__

        run: |
          ls wheels/clean/
          twine upload --non-interactive --skip-existing wheels/clean/*
