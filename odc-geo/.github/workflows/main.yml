name: Run Code Checks

on:
  pull_request:
  push:

jobs:
  build-wheels:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install Dependencies
        run: |
          python -m pip install --upgrade build
          python -m pip freeze

      - name: Build Clean Packages
        run: |
          mkdir -p ./wheels/clean
          python -m build
          mv dist/* ./wheels/clean/
          find ./wheels/clean -type f

      - name: Upload wheels as artifacts
        uses: actions/upload-artifact@v4
        with:
          name: wheels
          path: ./wheels/clean/
          retention-days: 1

  build-test-env-base:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - uses: actions/cache@v4
        id: conda_cache
        with:
          path: /tmp/test_env
          key: ${{ runner.os }}-test-env-py310-${{ hashFiles('tests/test-env-py310.yml') }}

      - uses: conda-incubator/setup-miniconda@v3
        if: steps.conda_cache.outputs.cache-hit != 'true'
        with:
          miniforge-version: latest
          channels: conda-forge,defaults
          activate-environment: ""

      - name: Dump Conda Environment Info
        shell: bash -l {0}
        if: steps.conda_cache.outputs.cache-hit != 'true'
        run: |
          conda info
          conda list
          mamba --version
          conda config --show-sources
          conda config --show
          printenv | sort

      - name: Build Python Environment for Testing
        shell: bash -l {0}
        if: steps.conda_cache.outputs.cache-hit != 'true'
        run: |
          mamba env create -f tests/test-env-py310.yml -p /tmp/test_env

      - name: Check Python Env
        shell: bash -l {0}
        if: steps.conda_cache.outputs.cache-hit != 'true'
        run: |
          mamba env export -p /tmp/test_env

  run-black-check:
    runs-on: ubuntu-latest
    needs:
      - build-test-env-base

    steps:
      - uses: actions/checkout@v4
      - name: Get Conda Environment from Cache
        uses: actions/cache@v4
        id: conda_cache
        with:
          path: /tmp/test_env
          key: ${{ runner.os }}-test-env-py310-${{ hashFiles('tests/test-env-py310.yml') }}

      - name: Update PATH
        shell: bash
        run: |
          echo "/tmp/test_env/bin" >> $GITHUB_PATH

      - name: Check formatting (black)
        shell: bash
        run: |
          black --check .

  run-pylint:
    runs-on: ubuntu-latest
    needs:
      - build-test-env-base

    steps:
      - uses: actions/checkout@v4
      - name: Get Conda Environment from Cache
        uses: actions/cache@v4
        id: conda_cache
        with:
          path: /tmp/test_env
          key: ${{ runner.os }}-test-env-py310-${{ hashFiles('tests/test-env-py310.yml') }}

      - name: Update PATH
        shell: bash
        run: |
          echo "/tmp/test_env/bin" >> $GITHUB_PATH

      - name: Install in Edit mode
        shell: bash
        run: |
          pip install -e . --no-deps

      - name: Check with pylint
        shell: bash
        run: |
          pylint -v odc

  run-mypy:
    runs-on: ubuntu-latest
    needs:
      - build-test-env-base

    steps:
      - uses: actions/checkout@v4
      - name: Get Conda Environment from Cache
        uses: actions/cache@v4
        id: conda_cache
        with:
          path: /tmp/test_env
          key: ${{ runner.os }}-test-env-py310-${{ hashFiles('tests/test-env-py310.yml') }}

      - name: Update PATH
        shell: bash
        run: |
          echo "/tmp/test_env/bin" >> $GITHUB_PATH

      - name: Check with mypy
        shell: bash
        run: |
          mypy --explicit-package-bases --python-version 3.10 odc

  test-with-coverage:
    runs-on: ubuntu-latest

    needs:
      - build-test-env-base
      - run-black-check

    steps:
      - uses: actions/checkout@v4

      - name: Get Conda Environment from Cache
        uses: actions/cache@v4
        id: conda_cache
        with:
          path: /tmp/test_env
          key: ${{ runner.os }}-test-env-py310-${{ hashFiles('tests/test-env-py310.yml') }}

      - name: Update PATH
        shell: bash
        run: |
          echo "/tmp/test_env/bin" >> $GITHUB_PATH

      - name: Install in Edit mode
        shell: bash
        run: |
          pip install -e . --no-deps

      - name: Run Tests
        shell: bash
        run: |
          echo "Running Tests"
          pytest --cov=odc \
          --cov-report=html \
          --cov-report=term \
          --cov-report=xml:coverage.xml \
          --timeout=30 \
          tests

        env:
          DASK_TEMPORARY_DIRECTORY: /tmp/dask

      - name: Upload Coverage
        if: |
          github.repository == 'opendatacube/odc-geo'

        uses: codecov/codecov-action@v4
        with:
          fail_ci_if_error: false
          verbose: false
          token: ${{ secrets.CODECOV_TOKEN }}

  test-wheels:
    runs-on: ubuntu-latest

    needs:
      - build-test-env-base
      - run-black-check
      - build-wheels

    steps:
      - uses: actions/checkout@v4

      - name: Download wheels from artifacts
        uses: actions/download-artifact@v4
        with:
          name: wheels
          path: ./wheels/clean

      - name: Get Conda Environment from Cache
        uses: actions/cache@v4
        id: conda_cache
        with:
          path: /tmp/test_env
          key: ${{ runner.os }}-test-env-py310-${{ hashFiles('tests/test-env-py310.yml') }}

      - name: Update PATH
        shell: bash
        run: |
          echo "/tmp/test_env/bin" >> $GITHUB_PATH

      - name: Install wheels for testing
        shell: bash
        run: |
          which python

          ls -lh wheels/clean
          python -m pip install --no-deps wheels/clean/*whl
          python -m pip check || true

      - name: Run Tests
        shell: bash
        run: |
          echo "Running Tests"
          pytest --timeout=30 tests

        env:
          DASK_TEMPORARY_DIRECTORY: /tmp/dask

  check-docs:
    runs-on: ubuntu-latest

    needs:
      - build-test-env-base
      - run-black-check

    steps:
      - uses: actions/checkout@v4

      - name: Get Conda Environment from Cache
        uses: actions/cache@v4
        id: conda_cache
        with:
          path: /tmp/test_env
          key: ${{ runner.os }}-test-env-py310-${{ hashFiles('tests/test-env-py310.yml') }}

      - name: Update PATH
        shell: bash
        run: |
          echo "/tmp/test_env/bin" >> $GITHUB_PATH

      - name: Install in Edit mode
        shell: bash
        run: |
          pip install -e . --no-deps

      - name: Build docs
        shell: bash
        run: |
          make -C docs html

      - name: Deploy to Netlify
        id: netlify
        if: github.event_name == 'pull_request'
        uses: nwtgck/actions-netlify@v3
        with:
          production-branch: "main"
          publish-dir: "docs/_build/html"
          deploy-message: "Deploy from GitHub Actions"
          github-token: ${{ secrets.GITHUB_TOKEN }}
          enable-pull-request-comment: true
          enable-commit-comment: false

        env:
          NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }}
          NETLIFY_SITE_ID: ${{ secrets.NETLIFY_SITE_ID }}

      - name: Print Notice
        uses: actions/github-script@v7
        if: github.event_name == 'pull_request'
        env:
          NETLIFY_URL: ${{ steps.netlify.outputs.deploy-url }}
        with:
          script: |
            const {NETLIFY_URL} = process.env
            core.notice(`Published to: ${NETLIFY_URL}`)
