name: odc-geo-tests-py310
channels:
  - conda-forge
  - nodefaults

dependencies:
  - python =3.10
  - pip >=25

  # odc-geo dependencies, optional also
  - pyproj
  - shapely >=2.0.1
  - rasterio >=1.3.7
  - xarray
  - numpy
  - dask >=2023.5.0
  - cachetools
  - tifffile >=2023.7.10

  ## linting tools
  - autopep8
  - autoflake
  - black >=25.1.0
  - isort
  - mypy
  - pycodestyle
  - pylint =3
  - types-cachetools
  - types-certifi

  ## test
  - pytest
  - pytest-cov
  - pytest-timeout
  - geopandas
  - folium
  - ipyleaflet
  - matplotlib-inline
  - ipykernel
  - ipython
  - rioxarray
  - azure-storage-blob
  - laspy
  - lazrs-python

  # for docs
  - sphinx
  - sphinx_rtd_theme >=1.2.0
  - sphinx-autodoc-typehints
  - jupyter-sphinx >=0.4.0
  - pip:
    - -e ../
    - types-shapely
