[project]
name = "odc-geo"
description = "Geometry Classes and Operations (opendatacube)"
version = "0.5.0rc1"
authors = [
    {name = "Open Data Cube"},
]
maintainers = [
    {name = "Open Data Cube"},
]
license = {text = "Apache License 2.0"}
readme = "README.rst"
keywords = ["gis", "geospatial", "opendatacube"]
classifiers = [
    "License :: OSI Approved :: Apache Software License",
    "Intended Audience :: Developers",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Scientific/Engineering :: GIS",
    "Typing :: Typed",
]
requires-python = ">=3.9"
dependencies = [
    "affine",
    "cachetools",
    "numpy",
    "pyproj>=3.0.0",
    "shapely",
    "xarray>=0.19",
]

[project.urls]
Homepage = "https://github.com/opendatacube/odc-geo/"
Documentation = "https://odc-geo.readthedocs.io/en/latest/"
"ODC Docs" = "https://opendatacube.readthedocs.io/en/latest/"
"Bug Reporting" = "https://github.com/opendatacube/odc-geo/issues"

[project.optional-dependencies]
xr = [] # xarray is now a non-optional dependency
warp = ["rasterio"]
tiff = ["tifffile", "imagecodecs", "dask[array,distributed]", "rasterio"]
s3 = ["boto3"]
az = ["azure-storage-blob"]
las = ["laspy", "lazrs"]
all = ["rasterio", "tifffile", "imagecodecs", "dask[array,distributed]", "boto3", "azure-storage-blob", "laspy", "lazrs"]

[build-system]
requires = ["flit_core >=3.2,<4"]
build-backend = "flit_core.buildapi"

[tool.flit.module]
name = "odc.geo"

[tool.mypy]
python_version = "3.9"
ignore_missing_imports = true
allow_redefinition = true

[tool.coverage.run]
omit = [
  "tests/*"
]

[tool.coverage.report]
exclude_lines = [
  "pragma: no cover",
  "^ *\\.\\.\\.$",
]

[tool.isort]
profile = "black"

[tool.pylint.messages_control]

max-line-length = 120
max-args = 15
max-positional-arguments = 12

disable = [
  "missing-function-docstring",
  "missing-module-docstring",
  "invalid-name",
  "fixme",
  "wrong-import-order",
  "cyclic-import",
  "ungrouped-imports",
  "wrong-import-position",
  "too-few-public-methods",
  "unsubscriptable-object",
]

[dependency-groups]
dev = [
    "azure-storage-blob>=12.25.0",
    "boto3>=1.37.21",
    "dask[array,distributed]>=2024.8.0",
    "folium>=0.19.5",
    "geopandas>=1.0.1",
    "imagecodecs>=2024.12.30",
    "ipyleaflet>=0.19.2",
    "matplotlib>=3.9.4",
    "pytest>=8.3.5",
    "pytest-cov>=6.0.0",
    "pytest-timeout>=2.3.1",
    "rasterio>=1.4.3",
    "tifffile>=2024.8.30",
    "xarray>=0.19",
    "laspy>=2.5.4",
    "lazrs>=0.6.2",
]
