name: odc-geo
channels:
  - nodefaults
  - conda-forge

dependencies:
  - python =3.10

  # odc-geo dependencies
  - pyproj
  - shapely
  - rasterio
  - xarray
  - numpy
  - toolz
  - cachetools
  - tifffile

  # tests and dev
  ## to use from jupyter-lab: `python -m ipykernel install --user --name odc-geo`
  - ipykernel
  - dask

  ## linting tools
  - autopep8
  - autoflake
  - black >=25.1
  - isort
  - mypy
  - pycodestyle
  - pylint =3
  - docutils

  ## test
  - pytest
  - pytest-cov
  - pytest-timeout
